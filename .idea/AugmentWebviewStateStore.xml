<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;3bf0e871-c0c0-4eec-a1e4-8f296a198595&quot;,&quot;conversations&quot;:{&quot;3bf0e871-c0c0-4eec-a1e4-8f296a198595&quot;:{&quot;id&quot;:&quot;3bf0e871-c0c0-4eec-a1e4-8f296a198595&quot;,&quot;createdAtIso&quot;:&quot;2025-08-02T08:02:08.487Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-02T08:02:08.487Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;5767a733-cfe1-4641-9475-2c3655ce992c&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>