from setuptools import find_packages, setup

package_name = 'py_srvcli'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools', 'pyserial'],
    zip_safe=True,
    maintainer='lwy',
    maintainer_email='<EMAIL>',
    description='Python client server tutorial',
    license='Apache License 2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'service = py_srvcli.service_member_function:main',
            'client = py_srvcli.client_member_function:main',
            'servo_service = py_srvcli.servo_controller_service:main',#ok
            'servo_client = py_srvcli.servo_controller_client:main',#ok
            'servo_service_fixed = py_srvcli.servo_service_fixed:main',
            'servo_client_fixed = py_srvcli.servo_client_fixed:main',
            'servo_service_robust = py_srvcli.servo_service_robust:main',
            'servo_service_optimized = py_srvcli.servo_controller_optimized_server:main',
            'servo_client_optimized = py_srvcli.servo_controller_optimized_client:main',
        ],
    },

)