#!/usr/bin/env python3
import sys
import select
import threading
from example_interfaces.srv import AddTwoInts
import rclpy
from rclpy.node import Node

class OptimizedServoControllerClient(Node):
    def __init__(self):
        super().__init__('optimized_servo_controller_client')
        self.cli = self.create_client(AddTwoInts, 'set_servo_angles')
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('服务不可用，正在等待...')
        self.req = AddTwoInts.Request()
        
        # 显示舵机角度限制信息
        self.get_logger().info("优化后的舵机控制客户端已就绪")
        self.get_logger().info("请输入两个角度，用逗号分隔 (例如: '45,-30')")
        self.get_logger().info("第一个角度控制上层舵机 (ID 0) (范围: -45° 至 +90°)")
        self.get_logger().info("第二个角度控制下层舵机 (ID 1) (范围: -120° 至 +120°)")
        self.get_logger().info("输入 'quit' 退出")
        
        # 启动用户输入线程
        self.running = True
        self.input_thread = threading.Thread(target=self.user_input_loop)
        self.input_thread.daemon = True
        self.input_thread.start()
    
    def user_input_loop(self):
        """用户输入循环"""
        while self.running and rclpy.ok():
            try:
                # 使用非阻塞输入
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    user_input = sys.stdin.readline().strip()
                    
                    if user_input.lower() in ['q', 'quit', 'exit']:
                        self.get_logger().info("正在退出...")
                        self.running = False
                        rclpy.shutdown()
                        return
                    
                    # 解析输入
                    try:
                        parts = user_input.split(',')
                        if len(parts) != 2:
                            raise ValueError("请输入两个用逗号分隔的角度值")
                        
                        upper_servo_angle = float(parts[0].strip())  # 上层舵机角度 (ID 0)
                        lower_servo_angle = float(parts[1].strip())  # 下层舵机角度 (ID 1)
                        
                        self.get_logger().info(f"发送请求: 上层舵机(ID 0) = {upper_servo_angle}°, 下层舵机(ID 1) = {lower_servo_angle}°")
                        
                        # 发送请求（乘以100转换为整数，保留两位小数精度）
                        self.send_request(int(upper_servo_angle * 100), int(lower_servo_angle * 100))
                    
                    except ValueError as e:
                        self.get_logger().error(f"输入无效: {str(e)}")
                        self.show_help()
            
            except Exception as e:
                self.get_logger().error(f"输入错误: {str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        self.get_logger().info("有效格式: 上层舵机角度,下层舵机角度 (例如: '45,-30')")
        self.get_logger().info("第一个角度控制上层舵机 (ID 0) (范围: -45° 至 +90°)")
        self.get_logger().info("第二个角度控制下层舵机 (ID 1) (范围: -120° 至 +120°)")
    
    def send_request(self, upper_servo_angle_int, lower_servo_angle_int):
        """发送舵机角度设置请求
        
        参数:
            upper_servo_angle_int: 上层舵机角度 (ID 0) (乘以100的整数)
            lower_servo_angle_int: 下层舵机角度 (ID 1) (乘以100的整数)
        """
        self.req.a = upper_servo_angle_int  # ID 0 - 上层舵机
        self.req.b = lower_servo_angle_int  # ID 1 - 下层舵机
        future = self.cli.call_async(self.req)
        future.add_done_callback(self.response_callback)
    
    def response_callback(self, future):
        """处理服务响应"""
        try:
            response = future.result()
            if response.sum == 1:
                self.get_logger().info("舵机移动成功")
            else:
                self.get_logger().error("舵机移动失败")
        except Exception as e:
            self.get_logger().error(f"服务调用失败: {str(e)}")

def main():
    rclpy.init()
    client = OptimizedServoControllerClient()
    
    try:
        rclpy.spin(client)
    except KeyboardInterrupt:
        pass
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 