#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
import time
import threading
import struct
import sys

# 尝试多种方式导入serial模块
try:
    import serial
except ImportError:
    try:
        import pyserial as serial
    except ImportError:
        print("错误: 无法导入serial模块。请确保已安装pyserial:")
        print("    pip install pyserial")
        print("或者:")
        print("    sudo apt install python3-serial")
        sys.exit(1)

from example_interfaces.srv import AddTwoInts

# ... 其余代码保持不变 ...

class ServoControllerService(Node):
    def __init__(self):
        super().__init__('servo_controller_service')
        
        # 参数配置
        self.declare_parameter('port', '/dev/ttyUSB0')
        self.declare_parameter('baudrate', 115200)
        self.declare_parameter('default_move_time', 500)  # 默认移动时间(ms)
        
        # # 舵机角度限制参数
        # self.declare_parameter('top_min_angle', -45.0)
        # self.declare_parameter('top_max_angle', 90.0)
        # self.declare_parameter('bottom_min_angle', -120.0)
        # self.declare_parameter('bottom_max_angle', 120.0)
        # 舵机角度限制参数
        self.declare_parameter('top_min_angle', -120.0)
        self.declare_parameter('top_max_angle', 120.0)
        self.declare_parameter('bottom_min_angle', -120.0)
        self.declare_parameter('bottom_max_angle', 120.0)
        
        # 获取参数
        port = self.get_parameter('port').value
        baudrate = self.get_parameter('baudrate').value
        self.default_move_time = self.get_parameter('default_move_time').value
        
        self.top_min_angle = self.get_parameter('top_min_angle').value
        self.top_max_angle = self.get_parameter('top_max_angle').value
        self.bottom_min_angle = self.get_parameter('bottom_min_angle').value
        self.bottom_max_angle = self.get_parameter('bottom_max_angle').value
        
        # 初始化串口
        self.serial_port = None
        self.serial_lock = threading.Lock()
        self.running = True
        self.servo_connection_status = {0: False, 1: False}  # 舵机连接状态
        
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            time.sleep(0.5)
            self.get_logger().info(f"已连接串口 {port}，波特率 {baudrate}")
            
            # 测试舵机连接并配置角度限制
            self.initialize_servos()
                
        except Exception as e:
            self.get_logger().error(f"串口连接错误: {str(e)}")
            raise
        
        # 创建服务
        self.srv = self.create_service(
            AddTwoInts, 
            'set_servo_angles', 
            self.set_servo_angles_callback)
        
        self.get_logger().info("舵机控制服务已就绪")
        self.get_logger().info(f"上层舵机角度范围: [{self.top_min_angle}, {self.top_max_angle}]")
        self.get_logger().info(f"下层舵机角度范围: [{self.bottom_min_angle}, {self.bottom_max_angle}]")
    
    # ... 其余代码保持不变 ...
    def initialize_servos(self):
        """初始化舵机连接并配置角度限制"""
        # 配置上层舵机角度限制
        if self.configure_angle_limits(0, self.top_min_angle, self.top_max_angle):
            self.servo_connection_status[0] = True
            self.get_logger().info(f"上层舵机 (ID 0) 已初始化，角度限制: {self.top_min_angle}° 至 {self.top_max_angle}°")
        
        # 配置下层舵机角度限制
        if self.configure_angle_limits(1, self.bottom_min_angle, self.bottom_max_angle):
            self.servo_connection_status[1] = True
            self.get_logger().info(f"下层舵机 (ID 1) 已初始化，角度限制: {self.bottom_min_angle}° 至 {self.bottom_max_angle}°")
    
    def configure_angle_limits(self, servo_id, min_angle, max_angle):
        """配置舵机角度限制"""
        # 1. 开启角度限制开关 (data_id=48, value=0x01)
        if not self.write_servo_parameter(servo_id, 48, 0x01):
            self.get_logger().error(f"无法启用舵机 ID {servo_id} 的角度限制")
            return False
        
        # 2. 设置角度下限 (data_id=52)
        min_angle_int = int(min_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 52, min_angle_int):
            self.get_logger().error(f"无法设置舵机 ID {servo_id} 的最小角度")
            return False
        
        # 3. 设置角度上限 (data_id=51)
        max_angle_int = int(max_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 51, max_angle_int):
            self.get_logger().error(f"无法设置舵机 ID {servo_id} 的最大角度")
            return False
        
        return True
    
    def write_servo_parameter(self, servo_id, data_id, value):
        """写入舵机参数 (指令ID=4 WRITE_DATA)"""
        # 构建内容: [servo_id, data_id, value(2字节小端)]
        content = servo_id.to_bytes(1, 'little')
        content += data_id.to_bytes(1, 'little')
        
        # 根据参数类型确定字节长度
        if data_id in [48, 49]:  # 单字节参数
            content += value.to_bytes(1, 'little')
        else:  # 双字节参数
            content += value.to_bytes(2, 'little', signed=True)
        
        with self.serial_lock:
            command = self.build_command(0x04, content)  # WRITE_DATA指令
            
            try:
                self.serial_port.write(command)
                self.get_logger().debug(f"参数写入命令已发送到 ID {servo_id}: {command.hex(' ')}")
                
                # 等待配置生效
                time.sleep(0.1)
                return True
            except Exception as e:
                self.get_logger().error(f"参数写入错误: {str(e)}")
                return False
    
    def set_servo_angles_callback(self, request, response):
        """服务回调函数，设置舵机角度"""
        # 解析请求中的角度值（已转换为浮点数）
        top_angle = request.a / 100.0  # 第一个参数是下层舵机角度
        bottom_angle = request.b / 100.0     # 第二个参数是上层舵机角度
        
        self.get_logger().info(f"收到请求: 下层={bottom_angle}°, 上层={top_angle}°")
        
        # 应用角度限制
        top_angle = max(self.top_min_angle, min(self.top_max_angle, top_angle))
        bottom_angle = max(self.bottom_min_angle, min(self.bottom_max_angle, bottom_angle))
        
        self.get_logger().info(f"移动下层舵机 (ID1) 到 {bottom_angle}°,移动上层舵机 (ID0) 到 {top_angle}°")
        # self.get_logger().info(f"移动上层舵机 (ID0) 到 {top_angle}°")
        
        
        # 同时控制两个舵机
        success0 = self.set_servo_angle(0, top_angle)
        time.sleep(0.05)  # 短暂延迟
        success1 = self.set_servo_angle(1, bottom_angle)
        
        if not success0 or not success1:
            self.check_servo_connection()
            response.sum = 0  # 表示失败
        else:
            response.sum = 1  # 表示成功
        
        return response
    
    def check_servo_connection(self):
        """检查舵机连接状态"""
        self.get_logger().warn("检查舵机连接状态...")
        
        # 检查上层舵机
        if self.ping_servo(0):
            if not self.servo_connection_status[0]:
                self.servo_connection_status[0] = True
                self.get_logger().info("上层舵机 (ID 0) 现已在线")
        else:
            self.servo_connection_status[0] = False
            self.get_logger().error("上层舵机 (ID 0) 无响应!")
        
        # 检查下层舵机
        if self.ping_servo(1):
            if not self.servo_connection_status[1]:
                self.servo_connection_status[1] = True
                self.get_logger().info("下层舵机 (ID 1) 现已在线")
        else:
            self.servo_connection_status[1] = False
            self.get_logger().error("下层舵机 (ID 1) 无响应!")
    
    def build_command(self, cmd_id, content):
        """构建符合协议的指令包"""
        header = b'\x12\x4C'
        length = len(content).to_bytes(1, 'little')
        cmd_id_byte = cmd_id.to_bytes(1, 'little')
        
        # 构建基础帧
        frame = header + cmd_id_byte + length + content
        
        # 计算校验和 (所有字节求和取低8位)
        checksum = sum(frame) & 0xFF
        frame += checksum.to_bytes(1, 'little')
        
        return frame
    
    def ping_servo(self, servo_id):
        """发送PING指令检查舵机是否在线"""
        with self.serial_lock:
            content = servo_id.to_bytes(1, 'little')
            command = self.build_command(0x01, content)  # PING指令
            
            try:
                self.serial_port.write(command)
                time.sleep(0.1)
                
                # 读取响应 (预期6字节: 05 1C 01 01 [ID] [Checksum])
                response = self.serial_port.read(6)
                if len(response) >= 6 and response[0] == 0x05 and response[1] == 0x1C:
                    # 验证响应的舵机ID
                    response_id = response[4]
                    if response_id == servo_id:
                        return True
                return False
            except Exception as e:
                self.get_logger().error(f"PING错误: {str(e)}")
                return False
    
    def set_servo_angle(self, servo_id, angle):
        """设置舵机角度 (指令ID 8)"""
        # 根据舵机ID应用不同的角度限制
        if servo_id == 0:  # 上层舵机
            angle = max(self.top_min_angle, min(self.top_max_angle, angle))
        elif servo_id == 1:  # 下层舵机
            angle = max(self.bottom_min_angle, min(self.bottom_max_angle, angle))
        else:
            # 默认限制
            angle = max(-180.0, min(180.0, angle))
        
        # 转换为0.1度单位的有符号整数
        angle_int = int(angle * 10)
        
        # 构建内容: [servo_id, angle(2字节小端有符号), move_time(2字节小端), power(2字节)]
        content = servo_id.to_bytes(1, 'little')
        content += angle_int.to_bytes(2, 'little', signed=True)
        content += self.default_move_time.to_bytes(2, 'little')
        content += b'\x00\x00'  # 功率设置为0 (使用默认)
        
        with self.serial_lock:
            command = self.build_command(0x08, content)  # MOVE_ON_ANGLE_MODE指令
            
            try:
                self.serial_port.write(command)
                self.get_logger().debug(f"命令已发送到 ID {servo_id}: {command.hex(' ')}")
                
                # 对于下层舵机，添加额外调试
                if servo_id == 1:
                    self.get_logger().debug(f"下层舵机命令详情: "
                                          f"角度={angle}° ({angle_int}), "
                                          f"时间={self.default_move_time}ms")
                
                return True
            except Exception as e:
                self.get_logger().error(f"发送到 ID {servo_id} 的写入错误: {str(e)}")
                return False
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            self.get_logger().info("串口已关闭")

def main():
    rclpy.init()
    node = ServoControllerService()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.cleanup()
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
