[0.885s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[1.223s] running egg_info
[1.223s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[1.223s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[1.224s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[1.224s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[1.224s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[1.227s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.227s] adding license file 'LICENSE'
[1.228s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.228s] running build
[1.228s] running build_py
[1.229s] copying py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.231s] running install
[1.231s] running install_lib
[1.234s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.234s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_server.py to servo_controller_optimized_server.cpython-310.pyc
[1.236s] running install_data
[1.237s] running install_egg_info
[1.240s] removing '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[1.240s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[1.242s] running install_scripts
[1.266s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.267s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.268s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.268s] Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.269s] Installing servo_client_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.270s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.270s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.271s] Installing servo_service_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.271s] Installing servo_service_robust script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.272s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[1.289s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
