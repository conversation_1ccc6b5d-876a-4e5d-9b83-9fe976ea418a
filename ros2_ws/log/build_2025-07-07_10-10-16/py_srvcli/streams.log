[0.902s] Invoking command in '/home/<USER>/ros2_ws/build/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build
[1.296s] running develop
[1.538s] Removing /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py-srvcli.egg-link (link to .)
[1.560s] Invoked command in '/home/<USER>/ros2_ws/build/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build
[1.565s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data --force
[1.897s] running egg_info
[1.898s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[1.899s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[1.899s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[1.900s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[1.900s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[1.903s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.903s] adding license file 'LICENSE'
[1.904s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.904s] running build
[1.904s] running build_py
[1.907s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.907s] copying py_srvcli/servo_service_fixed.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.907s] copying py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.908s] copying py_srvcli/servo_controller_optimized_client.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.908s] copying py_srvcli/servo_service_robust.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.910s] copying py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.910s] running install
[1.911s] running install_lib
[1.912s] creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.912s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.912s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/service_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.913s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/advanced_servo_controller_input2.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.914s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_service_fixed.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.914s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.915s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_client.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.915s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_service_robust.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.915s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.916s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/client_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.916s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.918s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[1.920s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/service_member_function.py to service_member_function.cpython-310.pyc
[1.922s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/advanced_servo_controller_input2.py to advanced_servo_controller_input2.cpython-310.pyc
[1.923s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_service_fixed.py to servo_service_fixed.cpython-310.pyc
[1.924s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_client.py to servo_controller_client.cpython-310.pyc
[1.925s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_client.py to servo_controller_optimized_client.cpython-310.pyc
[1.926s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_service_robust.py to servo_service_robust.cpython-310.pyc
[1.928s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc
[1.929s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/client_member_function.py to client_member_function.cpython-310.pyc
[1.929s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_server.py to servo_controller_optimized_server.cpython-310.pyc
[1.931s] running install_data
[1.932s] copying resource/py_srvcli -> /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
[1.932s] copying package.xml -> /home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli
[1.933s] running install_egg_info
[1.935s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[1.938s] running install_scripts
[1.960s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.961s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.962s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.963s] Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.963s] Installing servo_client_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.964s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.964s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.964s] Installing servo_service_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.964s] Installing servo_service_robust script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.966s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[1.984s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data --force
