[0.063s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.063s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x70a5ab0b5c00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x70a5ab2470a0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x70a5ab2470a0>>)
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.152s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.152s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/input_ctr_duoji/ros2_ws'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ros'
[0.162s] DEBUG:colcon.colcon_core.package_identification:Package 'src/py_srvcli' with type 'ros.ament_python' and name 'py_srvcli'
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.174s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.174s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.175s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/input_ctr_duoji/ros2_ws/install
[0.176s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_args' from command line to 'None'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_target' from command line to 'None'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_clean_cache' from command line to 'False'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_clean_first' from command line to 'False'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_force_configure' from command line to 'False'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'ament_cmake_args' from command line to 'None'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'catkin_cmake_args' from command line to 'None'
[0.202s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.202s] DEBUG:colcon.colcon_core.verb:Building package 'py_srvcli' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli', 'merge_install': False, 'path': '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli', 'symlink_install': False, 'test_result_base': None}
[0.202s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.203s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.203s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli' with build type 'ament_python'
[0.203s] Level 1:colcon.colcon_core.shell:create_environment_hook('py_srvcli', 'ament_prefix_path')
[0.204s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.204s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.ps1'
[0.205s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.dsv'
[0.205s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.sh'
[0.205s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.205s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.348s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli'
[0.349s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.349s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.534s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.696s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.697s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli' for CMake module files
[0.698s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli' for CMake config files
[0.698s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib'
[0.698s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/bin'
[0.698s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/pkgconfig/py_srvcli.pc'
[0.698s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages'
[0.698s] Level 1:colcon.colcon_core.shell:create_environment_hook('py_srvcli', 'pythonpath')
[0.699s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.ps1'
[0.699s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.dsv'
[0.699s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.sh'
[0.699s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/bin'
[0.699s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(py_srvcli)
[0.700s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/package.ps1'
[0.700s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/package.dsv'
[0.700s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/package.sh'
[0.701s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/package.bash'
[0.701s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/py_srvcli/package.zsh'
[0.701s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/share/colcon-core/packages/py_srvcli)
[0.702s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.702s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.702s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.702s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.705s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.705s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.705s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.713s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.713s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/input_ctr_duoji/ros2_ws/install/local_setup.ps1'
[0.713s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/input_ctr_duoji/ros2_ws/install/_local_setup_util_ps1.py'
[0.714s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/input_ctr_duoji/ros2_ws/install/setup.ps1'
[0.717s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/input_ctr_duoji/ros2_ws/install/local_setup.sh'
[0.718s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/input_ctr_duoji/ros2_ws/install/_local_setup_util_sh.py'
[0.719s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/input_ctr_duoji/ros2_ws/install/setup.sh'
[0.719s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/input_ctr_duoji/ros2_ws/install/local_setup.bash'
[0.719s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/input_ctr_duoji/ros2_ws/install/setup.bash'
[0.720s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/input_ctr_duoji/ros2_ws/install/local_setup.zsh'
[0.720s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/input_ctr_duoji/ros2_ws/install/setup.zsh'
