[0.890s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[1.226s] running egg_info
[1.226s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[1.226s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[1.227s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[1.227s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[1.227s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[1.230s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.231s] adding license file 'LICENSE'
[1.231s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[1.231s] running build
[1.232s] running build_py
[1.232s] copying py_srvcli/servo_controller_optimized_client.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.233s] copying py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[1.235s] running install
[1.236s] running install_lib
[1.236s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_client.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.237s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[1.238s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_client.py to servo_controller_optimized_client.cpython-310.pyc
[1.240s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_server.py to servo_controller_optimized_server.cpython-310.pyc
[1.243s] running install_data
[1.244s] running install_egg_info
[1.247s] removing '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[1.247s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[1.249s] running install_scripts
[1.273s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.273s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.274s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.275s] Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.275s] Installing servo_client_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.276s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.276s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.276s] Installing servo_service_optimized script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.277s] Installing servo_service_robust script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[1.277s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[1.295s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
