[0.098s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.098s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x79aee57560e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79aee58d65c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79aee58d65c0>>, mixin_verb=('build',))
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.236s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(src/py_srvcli) by extension 'ros'
[0.249s] DEBUG:colcon.colcon_core.package_identification:Package 'src/py_srvcli' with type 'ros.ament_python' and name 'py_srvcli'
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.273s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.273s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.274s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[0.276s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 342 installed packages in /opt/ros/humble
[0.278s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_args' from command line to 'None'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_target' from command line to 'None'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_clean_cache' from command line to 'False'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_clean_first' from command line to 'False'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'cmake_force_configure' from command line to 'False'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'ament_cmake_args' from command line to 'None'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'catkin_cmake_args' from command line to 'None'
[0.314s] Level 5:colcon.colcon_core.verb:set package 'py_srvcli' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.314s] DEBUG:colcon.colcon_core.verb:Building package 'py_srvcli' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/build/py_srvcli', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/install/py_srvcli', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/py_srvcli', 'symlink_install': True, 'test_result_base': None}
[0.314s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.315s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.316s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros2_ws/src/py_srvcli' with build type 'ament_python'
[0.316s] Level 1:colcon.colcon_core.shell:create_environment_hook('py_srvcli', 'ament_prefix_path')
[0.317s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.318s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.ps1'
[0.318s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.dsv'
[0.319s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/ament_prefix_path.sh'
[0.320s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.320s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.543s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros2_ws/src/py_srvcli'
[0.543s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.543s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.860s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/build/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
[1.238s] Level 1:colcon.colcon_core.shell:create_environment_hook('py_srvcli', 'pythonpath_develop')
[1.238s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/build/py_srvcli/share/py_srvcli/hook/pythonpath_develop.ps1'
[1.239s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/build/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
[1.239s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/build/py_srvcli/share/py_srvcli/hook/pythonpath_develop.dsv'
[1.240s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/build/py_srvcli/share/py_srvcli/hook/pythonpath_develop.sh'
[1.242s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli' for CMake module files
[1.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli' for CMake config files
[1.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli/lib'
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli/bin'
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli/lib/pkgconfig/py_srvcli.pc'
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages'
[1.244s] Level 1:colcon.colcon_core.shell:create_environment_hook('py_srvcli', 'pythonpath')
[1.244s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.ps1'
[1.245s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.dsv'
[1.245s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/hook/pythonpath.sh'
[1.246s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/install/py_srvcli/bin'
[1.246s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(py_srvcli)
[1.246s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/package.ps1'
[1.247s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/package.dsv'
[1.247s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/package.sh'
[1.248s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/package.bash'
[1.248s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli/package.zsh'
[1.249s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/install/py_srvcli/share/colcon-core/packages/py_srvcli)
[1.249s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.250s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.250s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.250s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.254s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.254s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.254s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.265s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.265s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.ps1'
[1.266s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_ps1.py'
[1.267s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.ps1'
[1.268s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.sh'
[1.269s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/install/_local_setup_util_sh.py'
[1.269s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.sh'
[1.270s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.bash'
[1.271s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.bash'
[1.272s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/install/local_setup.zsh'
[1.272s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/install/setup.zsh'
