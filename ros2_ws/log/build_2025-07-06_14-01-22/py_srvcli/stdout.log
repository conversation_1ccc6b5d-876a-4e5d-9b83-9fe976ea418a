running develop
running egg_info
writing py_srvcli.egg-info/PKG-INFO
writing dependency_links to py_srvcli.egg-info/dependency_links.txt
writing entry points to py_srvcli.egg-info/entry_points.txt
writing requirements to py_srvcli.egg-info/requires.txt
writing top-level names to py_srvcli.egg-info/top_level.txt
reading manifest file 'py_srvcli.egg-info/SOURCES.txt'
writing manifest file 'py_srvcli.egg-info/SOURCES.txt'
running build_ext
Creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py-srvcli.egg-link (link to .)
Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli

Installed /home/<USER>/ros2_ws/build/py_srvcli
running symlink_data
symbolically linking /home/<USER>/ros2_ws/build/py_srvcli/resource/py_srvcli -> /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
symbolically linking /home/<USER>/ros2_ws/build/py_srvcli/package.xml -> /home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli
