[0.545s] Invoking command in '/home/<USER>/ros2_ws/build/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
[0.754s] running develop
[0.886s] running egg_info
[0.886s] writing py_srvcli.egg-info/PKG-INFO
[0.887s] writing dependency_links to py_srvcli.egg-info/dependency_links.txt
[0.887s] writing entry points to py_srvcli.egg-info/entry_points.txt
[0.887s] writing requirements to py_srvcli.egg-info/requires.txt
[0.887s] writing top-level names to py_srvcli.egg-info/top_level.txt
[0.889s] reading manifest file 'py_srvcli.egg-info/SOURCES.txt'
[0.889s] writing manifest file 'py_srvcli.egg-info/SOURCES.txt'
[0.891s] running build_ext
[0.891s] Creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py-srvcli.egg-link (link to .)
[0.893s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.893s] 
[0.893s] Installed /home/<USER>/ros2_ws/build/py_srvcli
[0.893s] running symlink_data
[0.912s] Invoked command in '/home/<USER>/ros2_ws/build/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
