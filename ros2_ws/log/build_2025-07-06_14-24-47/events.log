[0.000000] (-) TimerEvent: {}
[0.000589] (py_srvcli) JobQueued: {'identifier': 'py_srvcli', 'dependencies': OrderedDict()}
[0.000637] (py_srvcli) JobStarted: {'identifier': 'py_srvcli'}
[0.099793] (-) TimerEvent: {}
[0.200092] (-) TimerEvent: {}
[0.300681] (-) TimerEvent: {}
[0.401213] (-) TimerEvent: {}
[0.501902] (-) TimerEvent: {}
[0.531331] (py_srvcli) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'develop', '--editable', '--build-directory', '/home/<USER>/ros2_ws/build/py_srvcli/build', '--no-deps', 'symlink_data'], 'cwd': '/home/<USER>/ros2_ws/build/py_srvcli', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'lwy', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'wayland', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/ros2_ws', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'HOMEBREW_PREFIX': '/home/<USER>/.linuxbrew', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'zh_CN.UTF-8', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1295', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=a6db92e1dbee40c87a1ab46c6869dee7', 'COLORTERM': 'truecolor', '_CE_M': '', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install', 'INFOPATH': '/home/<USER>/.linuxbrew/share/info:/home/<USER>/.linuxbrew/share/info:', 'ROS_DISTRO': 'humble', 'LOGNAME': 'lwy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'lwy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/anaconda3/condabin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/usr/bin/certutil:/usr/bin/certutil:/usr/bin/certutil', 'SESSION_MANAGER': 'local/lwy-machine:@/tmp/.ICE-unix/1295,unix/lwy-machine:/tmp/.ICE-unix/1295', 'HOMEBREW_CELLAR': '/home/<USER>/.linuxbrew/Cellar', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/26425457_b494_4861_aff1_b67c56c8e10a', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.ZG1C92', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'GNOME_TERMINAL_SERVICE': ':1.187', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/py_srvcli:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/build/py_srvcli', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=a6db92e1dbee40c87a1ab46c6869dee7', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:/home/<USER>/ros2_ws/build/py_srvcli:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'HOMEBREW_REPOSITORY': '/home/<USER>/.linuxbrew/Homebrew', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800'}, 'shell': False}
[0.602151] (-) TimerEvent: {}
[0.702652] (-) TimerEvent: {}
[0.754387] (py_srvcli) StdoutLine: {'line': b'running develop\n'}
[0.802932] (-) TimerEvent: {}
[0.886349] (py_srvcli) StdoutLine: {'line': b'running egg_info\n'}
[0.886514] (py_srvcli) StdoutLine: {'line': b'writing py_srvcli.egg-info/PKG-INFO\n'}
[0.887135] (py_srvcli) StdoutLine: {'line': b'writing dependency_links to py_srvcli.egg-info/dependency_links.txt\n'}
[0.887326] (py_srvcli) StdoutLine: {'line': b'writing entry points to py_srvcli.egg-info/entry_points.txt\n'}
[0.887893] (py_srvcli) StdoutLine: {'line': b'writing requirements to py_srvcli.egg-info/requires.txt\n'}
[0.888000] (py_srvcli) StdoutLine: {'line': b'writing top-level names to py_srvcli.egg-info/top_level.txt\n'}
[0.889272] (py_srvcli) StdoutLine: {'line': b"reading manifest file 'py_srvcli.egg-info/SOURCES.txt'\n"}
[0.890073] (py_srvcli) StdoutLine: {'line': b"writing manifest file 'py_srvcli.egg-info/SOURCES.txt'\n"}
[0.891358] (py_srvcli) StdoutLine: {'line': b'running build_ext\n'}
[0.891484] (py_srvcli) StdoutLine: {'line': b'Creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py-srvcli.egg-link (link to .)\n'}
[0.893246] (py_srvcli) StdoutLine: {'line': b'Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893348] (py_srvcli) StdoutLine: {'line': b'Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893406] (py_srvcli) StdoutLine: {'line': b'Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893460] (py_srvcli) StdoutLine: {'line': b'Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893513] (py_srvcli) StdoutLine: {'line': b'Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893566] (py_srvcli) StdoutLine: {'line': b'Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.893659] (py_srvcli) StdoutLine: {'line': b'\n'}
[0.893715] (py_srvcli) StdoutLine: {'line': b'Installed /home/<USER>/ros2_ws/build/py_srvcli\n'}
[0.893769] (py_srvcli) StdoutLine: {'line': b'running symlink_data\n'}
[0.903158] (-) TimerEvent: {}
[0.912235] (py_srvcli) CommandEnded: {'returncode': 0}
[0.923759] (py_srvcli) JobEnded: {'identifier': 'py_srvcli', 'rc': 0}
[0.925004] (-) EventReactorShutdown: {}
