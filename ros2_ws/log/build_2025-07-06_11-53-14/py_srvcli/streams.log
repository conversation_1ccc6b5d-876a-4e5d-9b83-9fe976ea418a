[0.543s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.736s] running egg_info
[0.736s] creating ../../build/py_srvcli/py_srvcli.egg-info
[0.736s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.736s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.737s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.737s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.737s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.737s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.739s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.739s] adding license file 'LICENSE'
[0.739s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.739s] running build
[0.739s] running build_py
[0.739s] creating /home/<USER>/ros2_ws/build/py_srvcli/build
[0.739s] creating /home/<USER>/ros2_ws/build/py_srvcli/build/lib
[0.739s] creating /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/service_member_function.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/advanced_servo_controller_input2.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/__init__.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.740s] copying py_srvcli/client_member_function.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.742s] running install
[0.742s] running install_lib
[0.742s] creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/service_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/advanced_servo_controller_input2.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/client_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.742s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[0.744s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/service_member_function.py to service_member_function.cpython-310.pyc
[0.745s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/advanced_servo_controller_input2.py to advanced_servo_controller_input2.cpython-310.pyc
[0.746s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_client.py to servo_controller_client.cpython-310.pyc
[0.747s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc
[0.747s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/client_member_function.py to client_member_function.cpython-310.pyc
[0.748s] running install_data
[0.748s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index
[0.748s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index
[0.748s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.748s] copying resource/py_srvcli -> /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.748s] copying package.xml -> /home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli
[0.749s] running install_egg_info
[0.750s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.751s] running install_scripts
[0.768s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.768s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.768s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.768s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.768s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[0.784s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': CONDA_PROMPT_MODIFIER=(base) PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
