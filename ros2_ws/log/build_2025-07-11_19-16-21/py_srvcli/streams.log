[0.357s] Invoking command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.484s] running egg_info
[0.484s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.492s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.492s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.493s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.493s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.494s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.494s] adding license file 'LICENSE'
[0.495s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.495s] running build
[0.495s] running build_py
[0.495s] copying py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.496s] running install
[0.496s] running install_lib
[0.497s] copying /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_optimized_server.py -> /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.497s] byte-compiling /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_optimized_server.py to servo_controller_optimized_server.cpython-310.pyc
[0.499s] running install_data
[0.499s] running install_egg_info
[0.500s] removing '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[0.500s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.501s] running install_scripts
[0.513s] Installing client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_client_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_client_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_service_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_service_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] Installing servo_service_robust script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.514s] writing list of installed files to '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log'
[0.527s] Invoked command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
