[0.535s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.730s] running egg_info
[0.730s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.730s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.730s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.730s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.731s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.732s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.733s] adding license file 'LICENSE'
[0.733s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.733s] running build
[0.733s] running build_py
[0.734s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.734s] running install
[0.735s] running install_lib
[0.735s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.736s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[0.738s] running install_data
[0.738s] running install_egg_info
[0.740s] removing '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[0.740s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.742s] running install_scripts
[0.758s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.758s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.758s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.758s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.759s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.759s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[0.776s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
