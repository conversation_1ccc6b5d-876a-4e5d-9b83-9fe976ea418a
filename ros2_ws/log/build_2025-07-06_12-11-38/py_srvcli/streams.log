[0.524s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.722s] running egg_info
[0.722s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.722s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.722s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.722s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.722s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.727s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.735s] adding license file 'LICENSE'
[0.735s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.736s] running build
[0.736s] running build_py
[0.736s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.736s] copying py_srvcli/servo_service_fixed.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.737s] running install
[0.737s] running install_lib
[0.738s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.740s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_service_fixed.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.740s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[0.741s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_service_fixed.py to servo_service_fixed.cpython-310.pyc
[0.742s] running install_data
[0.742s] running install_egg_info
[0.744s] removing '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[0.745s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.745s] running install_scripts
[0.762s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.762s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.762s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.763s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.763s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.763s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[0.780s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
