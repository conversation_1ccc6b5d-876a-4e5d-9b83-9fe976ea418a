[0.510s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.717s] running egg_info
[0.718s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.718s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.718s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.719s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.719s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.721s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.721s] adding license file 'LICENSE'
[0.721s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.721s] running build
[0.722s] running build_py
[0.723s] running install
[0.723s] running install_lib
[0.723s] running install_data
[0.724s] running install_egg_info
[0.726s] removing '/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[0.726s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.730s] running install_scripts
[0.745s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.745s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.745s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.745s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.745s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[0.761s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
