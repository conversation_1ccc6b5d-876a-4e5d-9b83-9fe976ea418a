[0.328s] Invoking command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.456s] running egg_info
[0.456s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.456s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.456s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.457s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.457s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.458s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.458s] adding license file 'LICENSE'
[0.458s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.458s] running build
[0.458s] running build_py
[0.458s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.459s] running install
[0.459s] running install_lib
[0.459s] copying /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.460s] byte-compiling /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[0.461s] running install_data
[0.461s] running install_egg_info
[0.462s] removing '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
[0.462s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.462s] running install_scripts
[0.475s] Installing client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_client_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_client_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_service_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_service_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] Installing servo_service_robust script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.475s] writing list of installed files to '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log'
[0.489s] Invoked command in '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build install --record /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
