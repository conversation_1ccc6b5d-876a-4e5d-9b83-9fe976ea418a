running egg_info
writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
adding license file 'LICENSE'
writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
running build
running build_py
copying py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli
running install
running install_lib
copying /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
byte-compiling /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
running install_data
running install_egg_info
removing '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)
Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
running install_scripts
Installing client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_client_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_client_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
Installing servo_service_robust script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli
writing list of installed files to '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log'
