[0.000000] (-) TimerEvent: {}
[0.000612] (py_srvcli) JobQueued: {'identifier': 'py_srvcli', 'dependencies': OrderedDict()}
[0.000656] (py_srvcli) JobStarted: {'identifier': 'py_srvcli'}
[0.099943] (-) TimerEvent: {}
[0.200226] (-) TimerEvent: {}
[0.300450] (-) TimerEvent: {}
[0.326449] (py_srvcli) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/py_srvcli', 'build', '--build-base', '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build', 'install', '--record', '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/input_ctr_duoji/ros2_ws/src/py_srvcli', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'HTTPS_PROXY': 'http://127.0.0.1:7890/', 'no_proxy': 'localhost,127.0.0.1,::1', 'LANGUAGE': 'zh_CN:en', 'USER': 'lwy', 'LC_TIME': 'zh_CN.UTF-8', 'all_proxy': 'socks://127.0.0.1:7890/', 'XDG_SESSION_TYPE': 'x11', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64::/home/<USER>/IsaacGym_Program/isaacgym/python/isaacgym/_bindings/linux-x86_64', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/input_ctr_duoji/ros2_ws/src', 'DESKTOP_SESSION': 'ubuntu', 'NO_PROXY': 'localhost,127.0.0.1,::1', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'zh_CN.UTF-8', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '1543', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=ec5ab617d2479ef742e6efbf68706c93', 'COLORTERM': 'truecolor', '_CE_M': '', 'https_proxy': 'http://127.0.0.1:7890/', 'COLCON_PREFIX_PATH': '/home/<USER>/input_ctr_duoji/ros2_ws/install', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'ibus', 'LOGNAME': 'lwy', 'ALL_PROXY': 'socks://127.0.0.1:7890/', 'http_proxy': 'http://127.0.0.1:7890/', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'lwy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/opt/ros/humble/bin:/home/<USER>/anaconda3/bin:/home/<USER>/anaconda3/condabin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/platform-tools-latest-linux/platform-tools', 'SESSION_MANAGER': 'local/lwy-Laptop:@/tmp/.ICE-unix/1543,unix/lwy-Laptop:/tmp/.ICE-unix/1543', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/2d5d83d4_6e46_45cc_aa0f_8dc76645e071', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'GNOME_TERMINAL_SERVICE': ':1.260', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli', 'CUDA_HOME': '/usr/local/cuda-12.1', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=ec5ab617d2479ef742e6efbf68706c93', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'HTTP_PROXY': 'http://127.0.0.1:7890/', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800'}, 'shell': False}
[0.400533] (-) TimerEvent: {}
[0.456503] (py_srvcli) StdoutLine: {'line': b'running egg_info\n'}
[0.456790] (py_srvcli) StdoutLine: {'line': b'writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO\n'}
[0.456963] (py_srvcli) StdoutLine: {'line': b'writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt\n'}
[0.457124] (py_srvcli) StdoutLine: {'line': b'writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt\n'}
[0.457171] (py_srvcli) StdoutLine: {'line': b'writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt\n'}
[0.457200] (py_srvcli) StdoutLine: {'line': b'writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt\n'}
[0.458307] (py_srvcli) StdoutLine: {'line': b"reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'\n"}
[0.458376] (py_srvcli) StdoutLine: {'line': b"adding license file 'LICENSE'\n"}
[0.458812] (py_srvcli) StdoutLine: {'line': b"writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'\n"}
[0.458884] (py_srvcli) StdoutLine: {'line': b'running build\n'}
[0.458917] (py_srvcli) StdoutLine: {'line': b'running build_py\n'}
[0.459032] (py_srvcli) StdoutLine: {'line': b'copying py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli\n'}
[0.459186] (py_srvcli) StdoutLine: {'line': b'running install\n'}
[0.459276] (py_srvcli) StdoutLine: {'line': b'running install_lib\n'}
[0.459722] (py_srvcli) StdoutLine: {'line': b'copying /home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli\n'}
[0.460212] (py_srvcli) StdoutLine: {'line': b'byte-compiling /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc\n'}
[0.461537] (py_srvcli) StdoutLine: {'line': b'running install_data\n'}
[0.461607] (py_srvcli) StdoutLine: {'line': b'running install_egg_info\n'}
[0.462571] (py_srvcli) StdoutLine: {'line': b"removing '/home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.462642] (py_srvcli) StdoutLine: {'line': b'Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info\n'}
[0.462959] (py_srvcli) StdoutLine: {'line': b'running install_scripts\n'}
[0.475457] (py_srvcli) StdoutLine: {'line': b'Installing client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475533] (py_srvcli) StdoutLine: {'line': b'Installing service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475567] (py_srvcli) StdoutLine: {'line': b'Installing servo_client script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475594] (py_srvcli) StdoutLine: {'line': b'Installing servo_client_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475623] (py_srvcli) StdoutLine: {'line': b'Installing servo_client_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475657] (py_srvcli) StdoutLine: {'line': b'Installing servo_service script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475778] (py_srvcli) StdoutLine: {'line': b'Installing servo_service_fixed script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475848] (py_srvcli) StdoutLine: {'line': b'Installing servo_service_optimized script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475884] (py_srvcli) StdoutLine: {'line': b'Installing servo_service_robust script to /home/<USER>/input_ctr_duoji/ros2_ws/install/py_srvcli/lib/py_srvcli\n'}
[0.475912] (py_srvcli) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/input_ctr_duoji/ros2_ws/build/py_srvcli/install.log'\n"}
[0.488906] (py_srvcli) CommandEnded: {'returncode': 0}
[0.494786] (py_srvcli) JobEnded: {'identifier': 'py_srvcli', 'rc': 0}
[0.495073] (-) EventReactorShutdown: {}
