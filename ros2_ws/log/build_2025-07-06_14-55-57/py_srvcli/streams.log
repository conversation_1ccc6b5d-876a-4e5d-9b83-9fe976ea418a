[0.532s] Invoking command in '/home/<USER>/ros2_ws/build/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
[0.749s] running develop
[0.877s] running egg_info
[0.877s] writing py_srvcli.egg-info/PKG-INFO
[0.877s] writing dependency_links to py_srvcli.egg-info/dependency_links.txt
[0.877s] writing entry points to py_srvcli.egg-info/entry_points.txt
[0.878s] writing requirements to py_srvcli.egg-info/requires.txt
[0.878s] writing top-level names to py_srvcli.egg-info/top_level.txt
[0.878s] reading manifest file 'py_srvcli.egg-info/SOURCES.txt'
[0.879s] writing manifest file 'py_srvcli.egg-info/SOURCES.txt'
[0.881s] running build_ext
[0.881s] Creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py-srvcli.egg-link (link to .)
[0.881s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing servo_client_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing servo_service_fixed script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] Installing servo_service_robust script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.883s] 
[0.883s] Installed /home/<USER>/ros2_ws/build/py_srvcli
[0.883s] running symlink_data
[0.900s] Invoked command in '/home/<USER>/ros2_ws/build/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros2_ws/build/py_srvcli/build --no-deps symlink_data
