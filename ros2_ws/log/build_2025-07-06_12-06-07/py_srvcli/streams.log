[0.567s] Invoking command in '/home/<USER>/ros2_ws/src/py_srvcli': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
[0.768s] running egg_info
[0.768s] creating ../../build/py_srvcli/py_srvcli.egg-info
[0.768s] writing ../../build/py_srvcli/py_srvcli.egg-info/PKG-INFO
[0.768s] writing dependency_links to ../../build/py_srvcli/py_srvcli.egg-info/dependency_links.txt
[0.768s] writing entry points to ../../build/py_srvcli/py_srvcli.egg-info/entry_points.txt
[0.768s] writing requirements to ../../build/py_srvcli/py_srvcli.egg-info/requires.txt
[0.768s] writing top-level names to ../../build/py_srvcli/py_srvcli.egg-info/top_level.txt
[0.769s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.769s] reading manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.769s] adding license file 'LICENSE'
[0.772s] writing manifest file '../../build/py_srvcli/py_srvcli.egg-info/SOURCES.txt'
[0.772s] running build
[0.772s] running build_py
[0.772s] creating /home/<USER>/ros2_ws/build/py_srvcli/build
[0.772s] creating /home/<USER>/ros2_ws/build/py_srvcli/build/lib
[0.772s] creating /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/service_member_function.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/advanced_servo_controller_input2.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/__init__.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] copying py_srvcli/client_member_function.py -> /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli
[0.772s] running install
[0.773s] running install_lib
[0.773s] creating /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.773s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_service.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.773s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/service_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.774s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/advanced_servo_controller_input2.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.774s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/servo_controller_client.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.774s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/__init__.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.775s] copying /home/<USER>/ros2_ws/build/py_srvcli/build/lib/py_srvcli/client_member_function.py -> /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli
[0.775s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_service.py to servo_controller_service.cpython-310.pyc
[0.776s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/service_member_function.py to service_member_function.cpython-310.pyc
[0.777s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/advanced_servo_controller_input2.py to advanced_servo_controller_input2.cpython-310.pyc
[0.778s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/servo_controller_client.py to servo_controller_client.cpython-310.pyc
[0.779s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/__init__.py to __init__.cpython-310.pyc
[0.779s] byte-compiling /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli/client_member_function.py to client_member_function.cpython-310.pyc
[0.779s] running install_data
[0.780s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index
[0.780s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index
[0.780s] creating /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.780s] copying resource/py_srvcli -> /home/<USER>/ros2_ws/install/py_srvcli/share/ament_index/resource_index/packages
[0.780s] copying package.xml -> /home/<USER>/ros2_ws/install/py_srvcli/share/py_srvcli
[0.780s] running install_egg_info
[0.782s] Copying ../../build/py_srvcli/py_srvcli.egg-info to /home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages/py_srvcli-0.0.0-py3.10.egg-info
[0.783s] running install_scripts
[0.799s] Installing client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.799s] Installing service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.800s] Installing servo_client script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.800s] Installing servo_service script to /home/<USER>/ros2_ws/install/py_srvcli/lib/py_srvcli
[0.800s] writing list of installed files to '/home/<USER>/ros2_ws/build/py_srvcli/install.log'
[0.816s] Invoked command in '/home/<USER>/ros2_ws/src/py_srvcli' returned '0': PYTHONPATH=/home/<USER>/ros2_ws/build/py_srvcli/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/py_srvcli/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/py_srvcli build --build-base /home/<USER>/ros2_ws/build/py_srvcli/build install --record /home/<USER>/ros2_ws/build/py_srvcli/install.log --single-version-externally-managed install_data
