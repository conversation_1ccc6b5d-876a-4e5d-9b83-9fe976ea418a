#!/usr/bin/env python3
import sys
import select
from example_interfaces.srv import AddTwoInts
import rclpy
from rclpy.node import Node

class ServoControllerClient(Node):
    def __init__(self):
        super().__init__('servo_controller_client')
        self.cli = self.create_client(AddTwoInts, 'set_servo_angles')
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('service not available, waiting again...')
        self.req = AddTwoInts.Request()
        
        self.get_logger().info("Servo controller client ready.")
        self.get_logger().info("Enter two angles separated by comma (e.g., '45,-30')")
        self.get_logger().info("First angle for top servo, second for bottom servo")
        self.get_logger().info("Type 'quit' to exit")
        
        # 启动用户输入线程
        self.running = True
        self.input_thread = threading.Thread(target=self.user_input_loop)
        self.input_thread.daemon = True
        self.input_thread.start()
    
    def user_input_loop(self):
        """用户输入循环"""
        while self.running and rclpy.ok():
            try:
                # 使用非阻塞输入
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    user_input = sys.stdin.readline().strip()
                    
                    if user_input.lower() in ['q', 'quit', 'exit']:
                        self.get_logger().info("Exiting...")
                        self.running = False
                        rclpy.shutdown()
                        return
                    
                    # 解析输入
                    try:
                        parts = user_input.split(',')
                        if len(parts) != 2:
                            raise ValueError("Please enter exactly two angles separated by comma")
                        
                        top_angle = float(parts[0].strip())
                        bottom_angle = float(parts[1].strip())
                        
                        # 发送请求（乘以100转换为整数，保留两位小数精度）
                        self.send_request(int(top_angle * 100), int(bottom_angle * 100))
                    
                    except ValueError as e:
                        self.get_logger().error(f"Invalid input: {str(e)}")
                        self.show_help()
            
            except Exception as e:
                self.get_logger().error(f"Input error: {str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        self.get_logger().info("Valid format: TOP_ANGLE,BOTTOM_ANGLE (e.g., '45,-30')")
        self.get_logger().info("First angle for top servo, second for bottom servo")
    
    def send_request(self, top_angle_int, bottom_angle_int):
        """发送舵机角度设置请求"""
        self.req.a = top_angle_int
        self.req.b = bottom_angle_int
        future = self.cli.call_async(self.req)
        future.add_done_callback(self.response_callback)
    
    def response_callback(self, future):
        """处理服务响应"""
        try:
            response = future.result()
            if response.sum == 1:
                self.get_logger().info("Servo movement successful")
            else:
                self.get_logger().error("Servo movement failed")
        except Exception as e:
            self.get_logger().error(f"Service call failed: {str(e)}")

def main():
    rclpy.init()
    client = ServoControllerClient()
    
    try:
        rclpy.spin(client)
    except KeyboardInterrupt:
        pass
    finally:
        client.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()