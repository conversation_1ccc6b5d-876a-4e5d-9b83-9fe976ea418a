#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
import serial
import time
import threading
import sys
import select
import struct

class OptimizedServoController(Node):
    def __init__(self):
        super().__init__('optimized_servo_controller')
        
        # 参数配置
        self.declare_parameter('port', '/dev/ttyUSB0')
        self.declare_parameter('baudrate', 115200)
        self.declare_parameter('default_move_time', 500)  # 默认移动时间(ms)
        
        # 舵机角度限制参数
        self.declare_parameter('top_min_angle', -120.0)
        self.declare_parameter('top_max_angle', 120.0)
        self.declare_parameter('bottom_min_angle', -45.0)
        self.declare_parameter('bottom_max_angle', 90.0)
        
        # 获取参数
        port = self.get_parameter('port').value
        baudrate = self.get_parameter('baudrate').value
        self.default_move_time = self.get_parameter('default_move_time').value
        
        self.top_min_angle = self.get_parameter('top_min_angle').value
        self.top_max_angle = self.get_parameter('top_max_angle').value
        self.bottom_min_angle = self.get_parameter('bottom_min_angle').value
        self.bottom_max_angle = self.get_parameter('bottom_max_angle').value
        
        # 初始化串口
        self.serial_port = None
        self.serial_lock = threading.Lock()
        self.running = True
        self.servo_connection_status = {0: False, 1: False}  # 舵机连接状态
        
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            time.sleep(0.5)
            self.get_logger().info(f"Connected to serial port {port} at {baudrate} baud")
            
            # 测试舵机连接并配置角度限制
            self.initialize_servos()
                
        except Exception as e:
            self.get_logger().error(f"Serial connection error: {str(e)}")
            raise
        
        # 启动用户输入线程
        self.input_thread = threading.Thread(target=self.user_input_loop)
        self.input_thread.daemon = True
        self.input_thread.start()
        
        self.get_logger().info("Optimized servo controller ready.")
        self.get_logger().info("Enter two angles separated by comma (e.g., '45,-30')")
        # self.get_logger().info("First angle for top servo (ID0), second for bottom servo (ID1)")
        self.get_logger().info("First angle for bottom servo (ID1), second for top servo (ID0)")
        self.get_logger().info("第一个角度是下层舵机，第二个角度是上层舵机")

        self.get_logger().info("Type 'quit' to exit")
        self.get_logger().info(f"Top servo angle range: [{self.top_min_angle}, {self.top_max_angle}]")
        self.get_logger().info(f"Bottom servo angle range: [{self.bottom_min_angle}, {self.bottom_max_angle}]")
    
    def initialize_servos(self):
        """初始化舵机连接并配置角度限制"""
        # 配置上层舵机角度限制
        if self.configure_angle_limits(0, self.top_min_angle, self.top_max_angle):
            self.servo_connection_status[0] = True
            self.get_logger().info(f"Top servo (ID 0) initialized with angle limits: {self.top_min_angle}° to {self.top_max_angle}°")
        
        # 配置下层舵机角度限制
        if self.configure_angle_limits(1, self.bottom_min_angle, self.bottom_max_angle):
            self.servo_connection_status[1] = True
            self.get_logger().info(f"Bottom servo (ID 1) initialized with angle limits: {self.bottom_min_angle}° to {self.bottom_max_angle}°")
    
    def configure_angle_limits(self, servo_id, min_angle, max_angle):
        """配置舵机角度限制"""
        # 1. 开启角度限制开关 (data_id=48, value=0x01)
        if not self.write_servo_parameter(servo_id, 48, 0x01):
            self.get_logger().error(f"Failed to enable angle limits for ID {servo_id}")
            return False
        
        # 2. 设置角度下限 (data_id=52)
        min_angle_int = int(min_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 52, min_angle_int):
            self.get_logger().error(f"Failed to set min angle for ID {servo_id}")
            return False
        
        # 3. 设置角度上限 (data_id=51)
        max_angle_int = int(max_angle * 10)  # 转换为0.1度单位
        if not self.write_servo_parameter(servo_id, 51, max_angle_int):
            self.get_logger().error(f"Failed to set max angle for ID {servo_id}")
            return False
        
        return True
    
    def write_servo_parameter(self, servo_id, data_id, value):
        """写入舵机参数 (指令ID=4 WRITE_DATA)"""
        # 构建内容: [servo_id, data_id, value(2字节小端)]
        content = servo_id.to_bytes(1, 'little')
        content += data_id.to_bytes(1, 'little')
        
        # 根据参数类型确定字节长度
        if data_id in [48, 49]:  # 单字节参数
            content += value.to_bytes(1, 'little')
        else:  # 双字节参数
            content += value.to_bytes(2, 'little', signed=True)
        
        with self.serial_lock:
            command = self.build_command(0x04, content)  # WRITE_DATA指令
            
            try:
                self.serial_port.write(command)
                self.get_logger().debug(f"Parameter write command sent to ID {servo_id}: {command.hex(' ')}")
                
                # 等待配置生效
                time.sleep(0.1)
                return True
            except Exception as e:
                self.get_logger().error(f"Write parameter error: {str(e)}")
                return False
    
    def user_input_loop(self):
        """用户输入循环"""
        while self.running and rclpy.ok():
            try:
                # 使用非阻塞输入
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    user_input = sys.stdin.readline().strip()
                    
                    if user_input.lower() in ['q', 'quit', 'exit']:
                        self.get_logger().info("Exiting...")
                        self.running = False
                        rclpy.shutdown()
                        return
                    
                    # 解析输入
                    try:
                        parts = user_input.split(',')
                        if len(parts) != 2:
                            raise ValueError("Please enter exactly two angles separated by comma")
                        
                        top_angle = float(parts[0].strip())
                        bottom_angle = float(parts[1].strip())
                        
                        # 应用角度限制
                        top_angle = max(self.top_min_angle, min(self.top_max_angle, top_angle))
                        bottom_angle = max(self.bottom_min_angle, min(self.bottom_max_angle, bottom_angle))
                        
                        self.get_logger().info(f"Moving top servo (ID0) to {top_angle}°")
                        self.get_logger().info(f"Moving bottom servo (ID1) to {bottom_angle}°")
                        
                        # 同时控制两个舵机
                        success0 = self.set_servo_angle(0, top_angle)
                        time.sleep(0.05)  # 短暂延迟
                        success1 = self.set_servo_angle(1, bottom_angle)
                        
                        if not success0 or not success1:
                            self.check_servo_connection()
                    
                    except ValueError as e:
                        self.get_logger().error(f"Invalid input: {str(e)}")
                        self.show_help()
            
            except Exception as e:
                self.get_logger().error(f"Input error: {str(e)}")
    
    def check_servo_connection(self):
        """检查舵机连接状态"""
        self.get_logger().warn("Checking servo connection status...")
        
        # 检查上层舵机
        if self.ping_servo(0):
            if not self.servo_connection_status[0]:
                self.servo_connection_status[0] = True
                self.get_logger().info("Top servo (ID 0) is now online")
        else:
            self.servo_connection_status[0] = False
            self.get_logger().error("Top servo (ID 0) is not responding!")
        
        # 检查下层舵机
        if self.ping_servo(1):
            if not self.servo_connection_status[1]:
                self.servo_connection_status[1] = True
                self.get_logger().info("Bottom servo (ID 1) is now online")
        else:
            self.servo_connection_status[1] = False
            self.get_logger().error("Bottom servo (ID 1) is not responding!")
    
    def show_help(self):
        """显示帮助信息"""
        self.get_logger().info("Valid format: ANGLE_TOP,ANGLE_BOTTOM (e.g., '45,-30')")
        self.get_logger().info("First angle for top servo (ID0), second for bottom servo (ID1)")
        self.get_logger().info(f"Top servo angle range: [{self.top_min_angle}, {self.top_max_angle}]")
        self.get_logger().info(f"Bottom servo angle range: [{self.bottom_min_angle}, {self.bottom_max_angle}]")
    
    def build_command(self, cmd_id, content):
        """构建符合协议的指令包"""
        header = b'\x12\x4C'
        length = len(content).to_bytes(1, 'little')
        cmd_id_byte = cmd_id.to_bytes(1, 'little')
        
        # 构建基础帧
        frame = header + cmd_id_byte + length + content
        
        # 计算校验和 (所有字节求和取低8位)
        checksum = sum(frame) & 0xFF
        frame += checksum.to_bytes(1, 'little')
        
        return frame
    
    def ping_servo(self, servo_id):
        """发送PING指令检查舵机是否在线"""
        with self.serial_lock:
            content = servo_id.to_bytes(1, 'little')
            command = self.build_command(0x01, content)  # PING指令
            
            try:
                self.serial_port.write(command)
                time.sleep(0.1)
                
                # 读取响应 (预期6字节: 05 1C 01 01 [ID] [Checksum])
                response = self.serial_port.read(6)
                if len(response) >= 6 and response[0] == 0x05 and response[1] == 0x1C:
                    # 验证响应的舵机ID
                    response_id = response[4]
                    if response_id == servo_id:
                        return True
                return False
            except Exception as e:
                self.get_logger().error(f"PING error: {str(e)}")
                return False
    
    def set_servo_angle(self, servo_id, angle):
        """设置舵机角度 (指令ID 8)"""
        # 根据舵机ID应用不同的角度限制
        if servo_id == 0:  # 上层舵机
            angle = max(self.top_min_angle, min(self.top_max_angle, angle))
        elif servo_id == 1:  # 下层舵机
            angle = max(self.bottom_min_angle, min(self.bottom_max_angle, angle))
        else:
            # 默认限制
            angle = max(-180.0, min(180.0, angle))
        
        # 转换为0.1度单位的有符号整数
        angle_int = int(angle * 10)
        
        # 构建内容: [servo_id, angle(2字节小端有符号), move_time(2字节小端), power(2字节)]
        content = servo_id.to_bytes(1, 'little')
        content += angle_int.to_bytes(2, 'little', signed=True)
        content += self.default_move_time.to_bytes(2, 'little')
        content += b'\x00\x00'  # 功率设置为0 (使用默认)
        
        with self.serial_lock:
            command = self.build_command(0x08, content)  # MOVE_ON_ANGLE_MODE指令
            
            try:
                self.serial_port.write(command)
                self.get_logger().info(f"Command sent to ID {servo_id}: {command.hex(' ')}")
                
                # 对于下层舵机，添加额外调试
                if servo_id == 1:
                    self.get_logger().debug(f"Bottom servo command details: "
                                          f"Angle={angle}° ({angle_int}), "
                                          f"Time={self.default_move_time}ms")
                
                return True
            except Exception as e:
                self.get_logger().error(f"Write error to ID {servo_id}: {str(e)}")
                return False
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            self.get_logger().info("Serial port closed")

def main(args=None):
    rclpy.init(args=args)
    controller = OptimizedServoController()
    
    try:
        rclpy.spin(controller)
    except KeyboardInterrupt:
        pass
    finally:
        controller.cleanup()
        controller.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()